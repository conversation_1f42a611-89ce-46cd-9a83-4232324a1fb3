// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

export class Config {
    /**
     * Creates a new Config instance.
     * @param {Partial<Config>} [$$source = {}] - The source object to create the Config.
     */
    constructor($$source = {}) {
        if (!("DebugMode" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["DebugMode"] = false;
        }
        if (!("ProfileFilePath" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["ProfileFilePath"] = "";
        }
        if (!("ResyncFilePath" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["ResyncFilePath"] = "";
        }
        if (!("RcloneFilePath" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["RcloneFilePath"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Config instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {Config}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Config(/** @type {Partial<Config>} */($$parsedSource));
    }
}
