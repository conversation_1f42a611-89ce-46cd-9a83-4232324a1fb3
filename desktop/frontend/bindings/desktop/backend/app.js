// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * App struct - now implements Wails v3 service interface
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Call as $Call, Create as $Create} from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as dto$0 from "./dto/models.js";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as models$0 from "./models/models.js";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as config$0 from "../../github.com/rclone/rclone/fs/config/models.js";

/**
 * @param {string} remoteName
 * @param {string} remoteType
 * @param {{ [_: string]: string }} remoteConfig
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function AddRemote(remoteName, remoteType, remoteConfig) {
    let $resultPromise = /** @type {any} */($Call.ByID(1805445590, remoteName, remoteType, remoteConfig));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @param {string} remoteName
 * @returns {Promise<void> & { cancel(): void }}
 */
export function DeleteRemote(remoteName) {
    let $resultPromise = /** @type {any} */($Call.ByID(2615584428, remoteName));
    return $resultPromise;
}

/**
 * Profile import/export functions
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function ExportProfiles() {
    let $resultPromise = /** @type {any} */($Call.ByID(1409002281));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * Remote import/export functions
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function ExportRemotes() {
    let $resultPromise = /** @type {any} */($Call.ByID(4058396298));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @returns {Promise<models$0.ConfigInfo> & { cancel(): void }}
 */
export function GetConfigInfo() {
    let $resultPromise = /** @type {any} */($Call.ByID(4029721521));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType2($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @returns {Promise<config$0.Remote[]> & { cancel(): void }}
 */
export function GetRemotes() {
    let $resultPromise = /** @type {any} */($Call.ByID(2690046198));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType4($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function ImportProfiles() {
    let $resultPromise = /** @type {any} */($Call.ByID(1366065666));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function ImportRemotes() {
    let $resultPromise = /** @type {any} */($Call.ByID(1434888639));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * File dialog functions
 * @param {string} title
 * @param {string[]} filters
 * @returns {Promise<[string, dto$0.AppError | null]> & { cancel(): void }}
 */
export function OpenFileDialog(title, filters) {
    let $resultPromise = /** @type {any} */($Call.ByID(865112495, title, filters));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        $result[1] = $$createType1($result[1]);
        return $result;
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @param {string} remoteName
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function OpenICloudSetup(remoteName) {
    let $resultPromise = /** @type {any} */($Call.ByID(2573002190, remoteName));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @param {string} title
 * @param {string} defaultFilename
 * @param {string[]} filters
 * @returns {Promise<[string, dto$0.AppError | null]> & { cancel(): void }}
 */
export function SaveFileDialog(title, defaultFilename, filters) {
    let $resultPromise = /** @type {any} */($Call.ByID(250231114, title, defaultFilename, filters));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        $result[1] = $$createType1($result[1]);
        return $result;
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function StopAddingRemote() {
    let $resultPromise = /** @type {any} */($Call.ByID(3347385390));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

/**
 * @param {number} id
 * @returns {Promise<void> & { cancel(): void }}
 */
export function StopCommand(id) {
    let $resultPromise = /** @type {any} */($Call.ByID(2441989574, id));
    return $resultPromise;
}

/**
 * @param {string} task
 * @param {models$0.Profile} profile
 * @returns {Promise<number> & { cancel(): void }}
 */
export function Sync(task, profile) {
    let $resultPromise = /** @type {any} */($Call.ByID(2952167798, task, profile));
    return $resultPromise;
}

/**
 * @param {string} task
 * @param {models$0.Profile} profile
 * @param {string} tabId
 * @returns {Promise<number> & { cancel(): void }}
 */
export function SyncWithTab(task, profile, tabId) {
    let $resultPromise = /** @type {any} */($Call.ByID(3515013809, task, profile, tabId));
    return $resultPromise;
}

/**
 * @param {string} task
 * @param {models$0.Profile} profile
 * @param {string} tabId
 * @returns {Promise<number> & { cancel(): void }}
 */
export function SyncWithTabId(task, profile, tabId) {
    let $resultPromise = /** @type {any} */($Call.ByID(1962241764, task, profile, tabId));
    return $resultPromise;
}

/**
 * @param {models$0.Profiles} profiles
 * @returns {Promise<dto$0.AppError | null> & { cancel(): void }}
 */
export function UpdateProfiles(profiles) {
    let $resultPromise = /** @type {any} */($Call.ByID(735983498, profiles));
    let $typingPromise = /** @type {any} */($resultPromise.then(($result) => {
        return $$createType1($result);
    }));
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

// Private type creation functions
const $$createType0 = dto$0.AppError.createFrom;
const $$createType1 = $Create.Nullable($$createType0);
const $$createType2 = models$0.ConfigInfo.createFrom;
const $$createType3 = config$0.Remote.createFrom;
const $$createType4 = $Create.Array($$createType3);
