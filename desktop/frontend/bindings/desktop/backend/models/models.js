// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as config$0 from "../config/models.js";

export class ConfigInfo {
    /**
     * Creates a new ConfigInfo instance.
     * @param {Partial<ConfigInfo>} [$$source = {}] - The source object to create the ConfigInfo.
     */
    constructor($$source = {}) {
        if (!("working_dir" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["working_dir"] = "";
        }
        if (!("selected_profile_index" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["selected_profile_index"] = 0;
        }
        if (!("profiles" in $$source)) {
            /**
             * @member
             * @type {Profiles}
             */
            this["profiles"] = (/** @type {Profiles} */([]));
        }
        if (!("env_config" in $$source)) {
            /**
             * @member
             * @type {config$0.Config}
             */
            this["env_config"] = (new config$0.Config());
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new ConfigInfo instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {ConfigInfo}
     */
    static createFrom($$source = {}) {
        const $$createField2_0 = $$createType0;
        const $$createField3_0 = $$createType3;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("profiles" in $$parsedSource) {
            $$parsedSource["profiles"] = $$createField2_0($$parsedSource["profiles"]);
        }
        if ("env_config" in $$parsedSource) {
            $$parsedSource["env_config"] = $$createField3_0($$parsedSource["env_config"]);
        }
        return new ConfigInfo(/** @type {Partial<ConfigInfo>} */($$parsedSource));
    }
}

export class Profile {
    /**
     * Creates a new Profile instance.
     * @param {Partial<Profile>} [$$source = {}] - The source object to create the Profile.
     */
    constructor($$source = {}) {
        if (!("name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["name"] = "";
        }
        if (!("from" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["from"] = "";
        }
        if (!("to" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["to"] = "";
        }
        if (!("included_paths" in $$source)) {
            /**
             * @member
             * @type {string[]}
             */
            this["included_paths"] = [];
        }
        if (!("excluded_paths" in $$source)) {
            /**
             * @member
             * @type {string[]}
             */
            this["excluded_paths"] = [];
        }
        if (!("bandwidth" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["bandwidth"] = 0;
        }
        if (!("parallel" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["parallel"] = 0;
        }
        if (!("backup_path" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["backup_path"] = "";
        }
        if (!("cache_path" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["cache_path"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Profile instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {Profile}
     */
    static createFrom($$source = {}) {
        const $$createField3_0 = $$createType4;
        const $$createField4_0 = $$createType4;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("included_paths" in $$parsedSource) {
            $$parsedSource["included_paths"] = $$createField3_0($$parsedSource["included_paths"]);
        }
        if ("excluded_paths" in $$parsedSource) {
            $$parsedSource["excluded_paths"] = $$createField4_0($$parsedSource["excluded_paths"]);
        }
        return new Profile(/** @type {Partial<Profile>} */($$parsedSource));
    }
}

/**
 * @typedef {Profile[]} Profiles
 */

// Private type creation functions
var $$createType0 = /** @type {(...args: any[]) => any} */(function $$initCreateType0(...args) {
    if ($$createType0 === $$initCreateType0) {
        $$createType0 = $$createType2;
    }
    return $$createType0(...args);
});
const $$createType1 = Profile.createFrom;
const $$createType2 = $Create.Array($$createType1);
const $$createType3 = config$0.Config.createFrom;
const $$createType4 = $Create.Array($Create.Any);
