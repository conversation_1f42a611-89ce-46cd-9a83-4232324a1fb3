// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

export class AppError {
    /**
     * Creates a new AppError instance.
     * @param {Partial<AppError>} [$$source = {}] - The source object to create the AppError.
     */
    constructor($$source = {}) {
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AppError instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AppError}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AppError(/** @type {Partial<AppError>} */($$parsedSource));
    }
}
