// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

/**
 * Remote defines a remote with a name, type, source and description
 */
export class Remote {
    /**
     * Creates a new Remote instance.
     * @param {Partial<Remote>} [$$source = {}] - The source object to create the Remote.
     */
    constructor($$source = {}) {
        if (!("name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["name"] = "";
        }
        if (!("type" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["type"] = "";
        }
        if (!("source" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["source"] = "";
        }
        if (!("description" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["description"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Remote instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {Remote}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Remote(/** @type {Partial<Remote>} */($$parsedSource));
    }
}
