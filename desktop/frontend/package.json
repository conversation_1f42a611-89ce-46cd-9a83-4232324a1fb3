{"name": "desktop", "version": "0.1.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "knip": "knip", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/common": "^20.0.6", "@angular/compiler": "^20.0.6", "@angular/core": "^20.0.6", "@angular/forms": "^20.0.6", "@angular/platform-browser": "^20.0.6", "@angular/platform-browser-dynamic": "^20.0.6", "@angular/router": "^20.0.6", "lucide-angular": "^0.525.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.5", "@angular/cli": "^20.0.5", "@angular/compiler-cli": "^20.0.6", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@types/jasmine": "~5.1.0", "angular-eslint": "19.0.2", "autoprefixer": "^10.4.21", "eslint": "^9.16.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "8.20.0"}, "packageManager": "yarn@4.9.2"}