version: "3"

includes:
  common: ./build/Taskfile.yml
  darwin: ./build/darwin/Taskfile.yml

vars:
  APP_NAME: ns-drive
  BIN_DIR: bin
  VITE_PORT: "{{.WAILS_VITE_PORT | default 9245}}"

tasks:
  # Build the application
  build:
    summary: Builds the application
    cmds:
      - task: "{{OS}}:build"

  # Package the application
  package:
    summary: Packages a production build of the application
    cmds:
      - task: "{{OS}}:package"

  # Run the application
  run:
    summary: Runs the application
    cmds:
      - task: "{{OS}}:run"

  # Development mode
  dev:
    summary: Runs the application in development mode
    cmds:
      - $(go env GOPATH)/bin/wails3 dev -config ./build/config.yml -port {{.VITE_PORT}}
