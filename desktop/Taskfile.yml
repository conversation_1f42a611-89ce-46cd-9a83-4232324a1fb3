version: '3'

vars:
  APP_NAME: ns-drive
  FRONTEND_DIR: frontend
  DIST_DIR: frontend/dist/browser
  BINARY_NAME: '{{.APP_NAME}}'

tasks:
  # Install frontend dependencies
  install-frontend:
    summary: Install frontend dependencies
    dir: '{{.FRONTEND_DIR}}'
    cmds:
      - yarn install

  # Build frontend for development
  build-frontend:
    summary: Build frontend for production
    dir: '{{.FRONTEND_DIR}}'
    cmds:
      - yarn build
    sources:
      - src/**/*
      - package.json
      - angular.json
      - tsconfig.json
    generates:
      - dist/browser/**/*

  # Build frontend for development with watch
  dev-frontend:
    summary: Build frontend for development with watch
    dir: '{{.FRONTEND_DIR}}'
    cmds:
      - yarn start

  # Generate bindings
  generate-bindings:
    summary: Generate TypeScript bindings from Go code
    cmds:
      - $(go env GOPATH)/bin/wails3 generate bindings

  # Pre-build tasks
  pre-build:
    summary: Pre-build tasks
    cmds:
      - task: install-frontend

  # Post-build tasks  
  post-build:
    summary: Post-build tasks
    cmds:
      - echo "Build completed successfully"

  # Development build
  dev:
    summary: Run the application in development mode
    cmds:
      - $(go env GOPATH)/bin/wails3 dev

  # Production build for current platform
  build:
    summary: Build the application for production
    cmds:
      - task: pre-build
      - task: build-frontend
      - task: generate-bindings
      - go build -ldflags="-s -w" -o bin/{{.BINARY_NAME}}
      - task: post-build

  # Build for macOS
  build:darwin:
    summary: Build the application for macOS
    platforms:
      - darwin
    cmds:
      - task: pre-build
      - task: build-frontend
      - task: generate-bindings
      - go build -ldflags="-s -w" -o bin/{{.BINARY_NAME}}
      - task: post-build
    env:
      CGO_CFLAGS: "-mmacosx-version-min=10.13"
      CGO_LDFLAGS: "-mmacosx-version-min=10.13"
      MACOSX_DEPLOYMENT_TARGET: "10.13"

  # Build for Windows
  build:windows:
    summary: Build the application for Windows
    platforms:
      - windows
    cmds:
      - task: pre-build
      - task: build-frontend
      - task: generate-bindings
      - go build -ldflags="-s -w -H windowsgui" -o bin/{{.BINARY_NAME}}.exe
      - task: post-build
    env:
      GOOS: windows
      GOARCH: amd64

  # Build for Linux
  build:linux:
    summary: Build the application for Linux
    platforms:
      - linux
    cmds:
      - task: pre-build
      - task: build-frontend
      - task: generate-bindings
      - go build -ldflags="-s -w" -o bin/{{.BINARY_NAME}}
      - task: post-build
    env:
      GOOS: linux
      GOARCH: amd64

  # Clean build artifacts
  clean:
    summary: Clean build artifacts
    cmds:
      - rm -rf bin/
      - rm -rf {{.DIST_DIR}}
      - rm -rf {{.FRONTEND_DIR}}/node_modules

  # Package the application
  package:
    summary: Package the application for distribution
    cmds:
      - $(go env GOPATH)/bin/wails3 package

  # Run tests
  test:
    summary: Run tests
    cmds:
      - go test ./...

  # Format code
  fmt:
    summary: Format Go code
    cmds:
      - go fmt ./...

  # Lint code
  lint:
    summary: Lint Go code
    cmds:
      - golangci-lint run

  # Install development tools
  install-tools:
    summary: Install development tools
    cmds:
      - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
      - $(go env GOPATH)/bin/wails3 doctor
